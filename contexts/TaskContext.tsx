import React, { createContext, ReactNode, useState, useEffect, useCallback } from 'react';
import { Task, Priority, RecurrenceType, TaskContextType } from '../types';
import { getNextRecurrenceDate } from '../utils/dateUtils';
import { apiService } from '../services/apiService';

export const TaskContext = createContext<TaskContextType | undefined>(undefined);

// Update seed tasks with current dates
const getInitialSeedTasks = (): Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>[] => {
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 15);
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);

    return [
        {
            title: "Renew Gym Membership",
            dueDate: nextMonth.toISOString().split('T')[0],
            dueTime: "10:00",
            isRecurring: true,
            recurrenceType: RecurrenceType.Yearly,
            recurrenceInterval: 1,
            category: 'Health',
            priority: Priority.Medium,
        },
        {
            title: "Monthly Project Report",
            dueDate: nextWeek.toISOString().split('T')[0],
            dueTime: "17:00",
            isRecurring: true,
            recurrenceType: RecurrenceType.Monthly,
            recurrenceInterval: 1,
            category: 'Work',
            priority: Priority.High,
        }
    ];
};


export const TaskProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [tasks, setTasks] = useState<Task[]>([]);
    const [isSeeding, setIsSeeding] = useState(true);

    const loadAndSeedTasks = useCallback(async () => {
        setIsSeeding(true);
        try {
            await apiService.seedInitialTasks(getInitialSeedTasks());
            const apiTasks = await apiService.getTasks();
            setTasks(apiTasks);
        } catch (error) {
            console.error('Failed to load tasks:', error);
        } finally {
            setIsSeeding(false);
        }
    }, []);

    useEffect(() => {
        loadAndSeedTasks();
    }, [loadAndSeedTasks]);

    const refreshTasks = async () => {
        try {
            const updatedTasks = await apiService.getTasks();
            setTasks(updatedTasks);
        } catch (error) {
            console.error('Failed to refresh tasks:', error);
        }
    };

    const addTask = async (task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>) => {
        try {
            await apiService.addTask(task);
            await refreshTasks();
        } catch (error) {
            console.error('Failed to add task:', error);
            throw error;
        }
    };
    
    const updateTask = async (updatedTask: Task) => {
        try {
            if (updatedTask.isRecurring && updatedTask.isCompleted) {
                const originalTask = tasks.find(t => t.id === updatedTask.id);
                if(originalTask) {
                    // Use a transaction to ensure both operations succeed or fail together
                    await apiService.runInTransaction(async () => {
                        // 1. Update the original recurring task for its next occurrence
                        const nextDueDate = getNextRecurrenceDate(originalTask);
                        await apiService.updateTask({ ...originalTask, dueDate: nextDueDate });
                        
                        // 2. Create a new, non-recurring, completed task instance
                        const completedInstance: Task = {
                            ...originalTask,
                            id: crypto.randomUUID(),
                            isRecurring: false,
                            isCompleted: true,
                            completedAt: new Date().toISOString(),
                            createdAt: new Date().toISOString(), 
                        };
                        await apiService.addFullTask(completedInstance);
                    });
                }
            } else {
                // Standard update for non-recurring tasks or other field changes
                const taskToSave = { ...updatedTask, completedAt: updatedTask.isCompleted ? new Date().toISOString() : undefined };
                await apiService.updateTask(taskToSave);
            }
            await refreshTasks();
        } catch (error) {
            console.error('Failed to update task:', error);
            throw error;
        }
    };

    const deleteTask = async (taskId: string) => {
        try {
            await apiService.deleteTask(taskId);
            await refreshTasks();
        } catch (error) {
            console.error('Failed to delete task:', error);
            throw error;
        }
    };
    
    const value = { tasks, addTask, updateTask, deleteTask, isSeeding };

    return (
        <TaskContext.Provider value={value}>
            {children}
        </TaskContext.Provider>
    );
};