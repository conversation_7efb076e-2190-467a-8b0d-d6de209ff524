import React, { createContext, ReactNode, useEffect, useState, useCallback } from 'react';
import { Settings } from '../types';
import { apiService } from '../services/apiService';

interface SettingsContextType {
    showCompleted: boolean;
    setShowCompleted: (show: boolean) => void;
}

export const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [settings, setSettings] = useState<Settings>({ showCompleted: true, seeded: false });

    const loadSettings = useCallback(async () => {
        try {
            const apiSettings = await apiService.getSettings();
            setSettings(apiSettings);
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }, []);

    useEffect(() => {
        loadSettings();
    }, [loadSettings]);

    const setShowCompleted = useCallback(async (show: boolean) => {
        try {
            setSettings(s => ({ ...s, showCompleted: show }));
            await apiService.updateSetting('showCompleted', show);
        } catch (error) {
            console.error('Failed to update showCompleted setting:', error);
            // Revert the optimistic update on error
            setSettings(s => ({ ...s, showCompleted: !show }));
            throw error;
        }
    }, []);

    const value = {
        showCompleted: settings.showCompleted,
        setShowCompleted
    };

    return (
        <SettingsContext.Provider value={value}>
            {children}
        </SettingsContext.Provider>
    );
};
